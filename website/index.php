<?php
/**
 * Point d'entrée client
 */

// Définition du chemin racine
define('ROOTPATH', dirname(__DIR__));

// La journalisation est maintenant gérée par le Logger centralisé via init.php.

// Vérification de l'installation
if (!file_exists(ROOTPATH . '/config/config.php')) {
    $baseUrl = rtrim(dirname(dirname($_SERVER['SCRIPT_NAME'])), '/');
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    header('Location: ' . $protocol . $host . $baseUrl . '/install');
    exit;
}

// Traitement spécial pour la route /license
$relativePath = '';
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$scriptName = dirname($_SERVER['SCRIPT_NAME']);
$relativePath = trim(str_replace($scriptName, '', $requestUri), '/');
if ($relativePath === 'license') {
    $distPath = __DIR__ . '/dist/index.html';
    if (file_exists($distPath)) {
        header('Content-Type: text/html; charset=UTF-8');
        readfile($distPath);
        exit;
    }
}

// Chargement de l'initialisation
require_once ROOTPATH . '/includes/init.php';

// Import des classes nécessaires
use TechCMS\Common\Core\Session;
use TechCMS\Common\Core\Router;
use TechCMS\Common\Core\Request;
use TechCMS\Common\Core\Logger;

// Utilisation de la session déjà démarrée
$session = Session::getInstance();

// Obtenir le chemin relatif de la requête
$requestUri = $_GET['original_uri'] ?? $_SERVER['REQUEST_URI'] ?? '';
if (empty($requestUri)) {
    // Si REQUEST_URI est vide, essayer de récupérer depuis REDIRECT_URL (cas de redirection .htaccess)
    $requestUri = $_SERVER['REDIRECT_URL'] ?? $_SERVER['SCRIPT_URL'] ?? '';
}
$requestUri = parse_url($requestUri, PHP_URL_PATH);
$scriptName = dirname($_SERVER['SCRIPT_NAME']);
$relativePath = trim(str_replace($scriptName, '', $requestUri), '/');

Logger::channel('app')->debug('Requête reçue dans website/index.php', ['uri' => $requestUri, 'path' => $relativePath]);

// Routes SPA spécifiques (store, pricing, etc.)
$spaRoutes = ['store', 'pricing', 'about', 'contact', 'license'];
$isKnownSpaRoute = false;
foreach ($spaRoutes as $route) {
    if (strpos($relativePath, $route) === 0) {
        $isKnownSpaRoute = true;
        break;
    }
}

if ($isKnownSpaRoute) {
    Logger::channel('app')->info("Route SPA détectée: {$relativePath} - Servir Vue.js");
}



// Si c'est un fichier statique dans /dist/assets, le servir directement
if (preg_match('/^assets\/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/', $relativePath)) {
    $filePath = __DIR__ . '/dist/' . $relativePath;
    Logger::channel('app')->debug("Tentative de servir le fichier statique: {$filePath}");
    
    if (file_exists($filePath)) {
        // Définir le type MIME approprié
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $mimeTypes = [
            'js' => 'application/javascript',
            'css' => 'text/css',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'ico' => 'image/x-icon',
            'svg' => 'image/svg+xml',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
        ];
        
        if (isset($mimeTypes[$extension])) {
            Logger::channel('app')->debug("Fichier trouvé, envoi avec type MIME: {$mimeTypes[$extension]}");
            header('Content-Type: ' . $mimeTypes[$extension]);
            header('Cache-Control: public, max-age=31536000'); // Cache 1 an
            readfile($filePath);
            exit;
        }
    } else {
        Logger::channel('app')->error("Fichier non trouvé: {$filePath}");
    }
}

// Si l'URL commence par /api, traiter comme une requête API
if (strpos($relativePath, 'api/') === 0 && $relativePath !== 'license') {
    Logger::channel('api')->debug("Traitement d'une requête API");
    
    try {
        $router = Router::getInstance();
        
        // Chargement des routes API
        require_once ROOTPATH . '/includes/routes/api.php';
        
        // Router la requête API
        $router->route($relativePath);
    } catch (Exception $e) {
        Logger::channel('api')->error('Erreur API: ' . $e->getMessage(), ['exception' => $e]);
        header('HTTP/1.1 500 Internal Server Error');
        echo json_encode(['error' => $e->getMessage()]);
    }
    exit;
}

// Pour toutes les autres requêtes, servir l'application Vue.js
$distPath = __DIR__ . '/dist/index.html';
Logger::channel('app')->info("Servir l'application Vue.js pour la route: {$relativePath}", [
    'path' => $relativePath,
    'dist_path' => $distPath
]);

if (file_exists($distPath)) {
    // Vérifier si l'utilisateur est connecté (via session PHP ou token JWT)
    $isAuthenticated = false;
    
    // 1. Vérification via session PHP traditionnelle
    if ($session->has('client')) {
        $isAuthenticated = true;
        Logger::channel('app')->info('Utilisateur authentifié via session PHP.');
    } 
    // 2. Vérification via cookie JWT
    else if (isset($_COOKIE['token'])) {
        try {
            // Vérifier le token JWT dans le cookie
            require_once ROOTPATH . '/api/v1/core/JWT.php';
            $payload = \TechCMS\Api\V1\Core\JWT::verify($_COOKIE['token']);
            if ($payload) {
                $isAuthenticated = true;
                Logger::channel('app')->info('Utilisateur authentifié via JWT cookie.');
            }
        } catch (\Exception $e) {
            Logger::channel('app')->warning('Erreur de vérification JWT (cookie)', ['exception' => $e->getMessage()]);
        }
    }
    // 3. Vérification via Authorization header (pour les requêtes API)
    else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            try {
                require_once ROOTPATH . '/api/v1/core/JWT.php';
                $payload = \TechCMS\Api\V1\Core\JWT::verify($matches[1]);
                if ($payload) {
                    $isAuthenticated = true;
                    Logger::channel('app')->info('Utilisateur authentifié via Authorization header.');
                }
            } catch (\Exception $e) {
                Logger::channel('app')->warning('Erreur de vérification JWT (header)', ['exception' => $e->getMessage()]);
            }
        }
    }
    
    // Le website est public - pas de redirection forcée vers login
    // L'authentification est optionnelle et gérée par les composants Vue.js
    
    // Servir l'application Vue.js
    Logger::channel('app')->debug("Envoi de l'application Vue.js.");
    header('Content-Type: text/html');
    readfile($distPath);
} else {
    Logger::channel('app')->error("Application Vue.js non trouvée.", ['path' => $distPath]);
    header('HTTP/1.1 404 Not Found');
    echo 'Application non trouvée. Avez-vous exécuté npm run build ?';
}
