<?php
/**
 * Gestion des tokens API
 */

namespace TechCMS\Api\V1\Models;

use TechCMS\Common\Core\Logger;
use TechCMS\Admin\Models\AdminModel;
use TechCMS\Common\Models\BaseModel;

class ApiTokenModel extends BaseModel {
    protected $table = 'api_tokens';
    protected $timestamps = false;
    protected $fillable = [
        'user_id',
        'name',
        'token',
        'permissions',
        'last_used_at',
        'expires_at',
        'created_at'
    ];

    /**
     * Crée un nouveau token API
     */
    public function createToken($userId, $name, $permissions = [], $expiresAt = null) {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        $data = [
            'user_id' => $userId,
            'name' => $name,
            'token' => $hashedToken,
            'permissions' => json_encode($permissions),
            'last_used_at' => null,
            'expires_at' => $expiresAt,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->create($data);
        
        if ($result) {
            return [
                'id' => $result['id'],
                'token' => $token,
                'name' => $name,
                'permissions' => $permissions,
                'expires_at' => $expiresAt
            ];
        }

        return false;
    }

    /**
     * Vérifie si un token est valide
     */
    public function validateToken($token) {
        if (!$token) {
            Logger::channel('api')->warning('Validation failed: Token was empty or null.');
            return false;
        }
        
        // Vérifier si c'est un JWT
        if (strpos($token, 'eyJ') === 0 && strpos($token, '.') !== false) {
            return $this->validateJWT($token);
        }
        
        // Sinon, c'est un token classique
        $hashedToken = hash('sha256', $token);
        
        $sql = "SELECT t.*, a.id as user_id, a.email, a.status 
                FROM {$this->table} t 
                JOIN admins a ON t.user_id = a.id 
                WHERE t.token = ? AND (t.expires_at IS NULL OR t.expires_at > NOW())
                AND a.status = 'active'
                LIMIT 1";
                
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$hashedToken]);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($result) {
            // Mise à jour du last_used_at
            $this->update($result['id'], ['last_used_at' => date('Y-m-d H:i:s')]);
            return $result;
        }
        
        Logger::channel('api')->warning('Validation failed: No valid token found in database.', ['hashed_token' => $hashedToken]);
        return false;
    }
    
    /**
     * Valide un token JWT (spécifiquement pour les admins)
     */
    private function validateJWT($token) {
        try {
            // Vérifier si la classe JWT existe
            if (!class_exists('\\TechCMS\\Api\\V1\\Core\\JWT')) {
                Logger::channel('api')->critical('JWT validation failed: JWT Core class not found.');
                return false;
            }

            // Utiliser la méthode statique decode
            $decoded = \TechCMS\Api\V1\Core\JWT::decode($token);

            if (!$decoded || !isset($decoded->sub)) {
                Logger::channel('api')->warning('JWT validation failed: Invalid or malformed token.');
                return false;
            }

            // Déterminer le type d'utilisateur (admin ou client)
            $userType = isset($decoded->user_type) ? $decoded->user_type : (isset($decoded->type) ? $decoded->type : 'undefined');

            if ($userType === 'admin') {
                // Validation pour token admin
                $userModel = new AdminModel();
                $user = $userModel->find($decoded->sub);

                if (!$user || $user['status'] !== 'active') {
                    Logger::channel('api')->warning('JWT validation failed: Admin user is inactive or does not exist.', ['user_id' => $decoded->sub]);
                    return false;
                }

                // Créer un résultat pour admin
                $result = [
                    'id' => null,
                    'user_id' => $decoded->sub,
                    'email' => $user['email'],
                    'status' => $user['status'],
                    'token_type' => 'jwt',
                    'user_type' => 'admin'
                ];

            } elseif ($userType === 'client') {
                // Validation pour token client
                $clientModel = new \TechCMS\Common\Models\ClientModel();
                $client = $clientModel->find($decoded->sub);

                if (!$client || $client['status'] !== 'active') {
                    Logger::channel('api')->warning('JWT validation failed: Client user is inactive or does not exist.', ['user_id' => $decoded->sub]);
                    return false;
                }

                // Créer un résultat pour client
                $result = [
                    'id' => null,
                    'user_id' => $decoded->sub,
                    'email' => $client['email'],
                    'status' => $client['status'],
                    'token_type' => 'jwt',
                    'user_type' => 'client'
                ];

            } else {
                Logger::channel('api')->warning('JWT validation failed: Invalid user type in token.', ['user_type' => $userType, 'user_id' => $decoded->sub]);
                return false;
            }

            return $result;
        } catch (\Exception $e) {
            Logger::channel('api')->error('JWT validation failed with an exception.', ['exception' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Révoque un token
     */
    public function revokeToken($id, $userId) {
        return $this->delete($id, ['user_id' => $userId]);
    }

    /**
     * Liste les tokens d'un utilisateur
     */
    public function listUserTokens($userId) {
        return $this->findAll(['user_id' => $userId], ['created_at' => 'DESC']);
    }
}
