<?php
/**
 * Routes API publiques pour la vitrine
 * 
 * @package TechCMS
 * @version 1.0.0
 */

use TechCMS\Api\V1\Controllers\website\WebsiteTemplateController;
use TechCMS\Api\V1\Controllers\website\WebsiteContactController;
use TechCMS\Api\V1\Controllers\website\WebsiteAuthController;
use TechCMS\Api\V1\Controllers\website\WebsiteLogController;
use TechCMS\Common\Core\Router;

// Obtenir l'instance du routeur
$router = Router::getInstance();

// Instancier les contrôleurs
$templateController = new WebsiteTemplateController();
$contactController = new WebsiteContactController();
$authController = new WebsiteAuthController();
$logController = new WebsiteLogController();

// Groupe pour les routes vitrine (publiques, sans authentification)
$router->group('/website', [], function() use ($router, $templateController, $contactController, $authController, $logController) {
    
    // Routes pour les templates
    $router->group('/templates', [], function() use ($router, $templateController) {
        
        // GET /api/v1/website/templates - Liste des templates actifs
        $router->get('', [$templateController, 'index']);
        
        // GET /api/v1/website/templates/featured - Templates mis en avant
        $router->get('/featured', [$templateController, 'featured']);
        
        // GET /api/v1/website/templates/stats - Statistiques publiques
        $router->get('/stats', [$templateController, 'stats']);
        
        // GET /api/v1/website/templates/{id} - Détail d'un template
        $router->get('/{id}', [$templateController, 'show']);
        
    });
    
    // Routes pour le contact
    $router->group('/contact', [], function() use ($router, $contactController) {

        // POST /api/v1/website/contact - Envoyer un message de contact
        $router->post('', [$contactController, 'send']);

    });

    // Routes pour l'authentification
    $router->group('/auth', [], function() use ($router, $authController) {

        // POST /api/v1/website/auth/login - Connexion client
        $router->post('/login', [$authController, 'login']);

        // POST /api/v1/website/auth/register - Inscription client
        $router->post('/register', [$authController, 'register']);

        // GET /api/v1/website/auth/me - Profil utilisateur connecté
        $router->get('/me', [$authController, 'me']);

        // POST /api/v1/website/auth/logout - Déconnexion
        $router->post('/logout', [$authController, 'logout']);

    });

    // Route pour le token Ably temps réel website (authentification requise)
    $router->get('/realtime/token', [$authController, 'getRealtimeToken']);

    // Routes pour les logs frontend
    $router->group('/logs', [], function() use ($router, $logController) {

        // POST /api/v1/website/logs - Enregistrer un log frontend
        $router->post('', [$logController, 'store']);

        // POST /api/v1/website/logs/batch - Enregistrer plusieurs logs
        $router->post('/batch', [$logController, 'batch']);

    });

});
